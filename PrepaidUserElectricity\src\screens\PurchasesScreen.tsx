import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TextInput,
  TouchableOpacity,
  Alert,
  FlatList,
  RefreshControl,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { useAppDispatch, useAppSelector } from '../store';
import { loadPurchases, addPurchase, setLivePreview } from '../store/slices/purchasesSlice';
import { loadSettings } from '../store/slices/settingsSlice';
import { Purchase } from '../types';
import { notificationService } from '../services/notificationService';
import { useTheme } from '../context/ThemeContext';
import ThemedButton from '../components/ThemedButton';

const PurchasesScreen = () => {
  const dispatch = useAppDispatch();
  const { currentTheme } = useTheme();
  const [currencyAmount, setCurrencyAmount] = useState('');
  const [refreshing, setRefreshing] = useState(false);

  // Get data from Redux store
  const purchasesState = useAppSelector((state) => state.purchases);
  const settingsState = useAppSelector((state) => state.settings);

  const { purchases, isLoading, livePreview } = purchasesState;
  const {
    config: {
      costPerUnit,
      selectedCurrency,
      selectedUnit,
    }
  } = settingsState;

  useEffect(() => {
    const loadData = async () => {
      try {
        await dispatch(loadSettings()).unwrap();
        await dispatch(loadPurchases()).unwrap();
      } catch (error) {
        console.error('Error loading data:', error);
        Alert.alert('Error', 'Failed to load data');
      }
    };

    loadData();
  }, [dispatch]);

  // Update live preview when currency amount changes
  useEffect(() => {
    const currency = parseFloat(currencyAmount) || 0;
    const units = currency / costPerUnit;
    dispatch(setLivePreview({
      currencyAmount: currency,
      unitAmount: units,
      costPerUnit,
    }));
  }, [currencyAmount, costPerUnit, dispatch]);

  const onRefresh = async () => {
    setRefreshing(true);
    try {
      await dispatch(loadPurchases()).unwrap();
    } catch (error) {
      console.error('Error refreshing purchases:', error);
      Alert.alert('Error', 'Failed to refresh purchases');
    } finally {
      setRefreshing(false);
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - date.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 1) {
      return `Today, ${date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}`;
    } else if (diffDays === 2) {
      return `Yesterday, ${date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}`;
    } else {
      return date.toLocaleDateString() + ', ' + date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    }
  };

  const renderPurchaseItem = ({ item }: { item: Purchase }) => (
    <View style={[styles.purchaseItem, { borderBottomColor: currentTheme.colors.surface }]}>
      <View style={styles.purchaseHeader}>
        <Icon name="shopping-cart" size={20} color={currentTheme.colors.primary} />
        <Text style={[styles.purchaseDate, { color: currentTheme.colors.textSecondary }]}>
          {formatDate(item.purchase_date)}
        </Text>
      </View>
      <View style={styles.purchaseDetails}>
        <Text style={[styles.purchaseAmount, { color: currentTheme.colors.text }]}>
          {item.currency_type} {item.currency_amount.toFixed(2)} → {item.unit_amount.toFixed(2)} {item.unit_type}
        </Text>
        <Text style={[styles.purchaseRate, { color: currentTheme.colors.textSecondary }]}>
          Rate: {item.currency_type} {item.cost_per_unit.toFixed(3)}/{item.unit_type}
        </Text>
      </View>
    </View>
  );

  const handleSavePurchase = async () => {
    if (!currencyAmount || parseFloat(currencyAmount) <= 0) {
      Alert.alert('Error', 'Please enter a valid currency amount');
      return;
    }

    try {
      const purchase: Omit<Purchase, 'id'> = {
        currency_amount: parseFloat(currencyAmount),
        unit_amount: livePreview.unitAmount,
        currency_type: selectedCurrency.code,
        unit_type: selectedUnit.symbol,
        cost_per_unit: costPerUnit,
        purchase_date: new Date().toISOString(),
        notes: `Purchase of ${livePreview.unitAmount.toFixed(2)} ${selectedUnit.symbol} for ${selectedCurrency.symbol} ${currencyAmount}`,
      };

      await dispatch(addPurchase(purchase)).unwrap();

      // Send notification
      await notificationService.sendPurchaseConfirmation(
        parseFloat(currencyAmount),
        livePreview.unitAmount,
        selectedCurrency.code,
        selectedUnit.symbol
      );

      Alert.alert(
        'Purchase Saved',
        `Saved: ${currencyAmount} ${selectedCurrency.symbol} for ${livePreview.unitAmount.toFixed(2)} ${selectedUnit.symbol}`,
        [
          {
            text: 'OK',
            onPress: () => {
              setCurrencyAmount('');
            },
          },
        ]
      );
    } catch (error) {
      console.error('Error saving purchase:', error);
      Alert.alert('Error', 'Failed to save purchase. Please try again.');
    }
  };

  const themedStyles = {
    container: {
      ...styles.container,
      backgroundColor: currentTheme.colors.background,
    },
    inputContainer: {
      ...styles.inputContainer,
      backgroundColor: currentTheme.colors.surface,
    },
    sectionTitle: {
      ...styles.sectionTitle,
      color: currentTheme.colors.text,
    },
    inputLabel: {
      ...styles.inputLabel,
      color: currentTheme.colors.primary,
    },
    input: {
      ...styles.input,
      borderColor: currentTheme.colors.primary,
      backgroundColor: currentTheme.colors.background,
      color: currentTheme.colors.text,
    },
    recentContainer: {
      ...styles.recentContainer,
      backgroundColor: currentTheme.colors.surface,
    },
  };

  return (
    <ScrollView style={themedStyles.container}>
      {/* Input Section */}
      <View style={themedStyles.inputContainer}>
        <Text style={themedStyles.sectionTitle}>Add New Purchase</Text>

        {/* Currency Input */}
        <View style={styles.inputGroup}>
          <Text style={themedStyles.inputLabel}>
            <Icon name="attach-money" size={16} color={currentTheme.colors.primary} />
            Currency Amount ({selectedCurrency.symbol})
          </Text>
          <TextInput
            style={themedStyles.input}
            value={currencyAmount}
            onChangeText={setCurrencyAmount}
            placeholder="Enter amount spent"
            keyboardType="numeric"
            placeholderTextColor={currentTheme.colors.textSecondary}
          />
        </View>

        {/* Live Preview */}
        <View style={[styles.previewContainer, {
          backgroundColor: currentTheme.colors.success + '20',
          borderColor: currentTheme.colors.success
        }]}>
          <Text style={[styles.previewTitle, { color: currentTheme.colors.success }]}>
            <Icon name="visibility" size={16} color={currentTheme.colors.success} /> Live Preview
          </Text>
          <View style={styles.previewContent}>
            <View style={styles.previewRow}>
              <Text style={[styles.previewLabel, { color: currentTheme.colors.textSecondary }]}>
                Currency Amount:
              </Text>
              <Text style={[styles.previewValue, { color: currentTheme.colors.text }]}>
                {selectedCurrency.symbol} {livePreview.currencyAmount.toFixed(2)}
              </Text>
            </View>
            <View style={styles.previewRow}>
              <Text style={[styles.previewLabel, { color: currentTheme.colors.textSecondary }]}>
                Units Purchased:
              </Text>
              <Text style={[styles.previewValue, { color: currentTheme.colors.text }]}>
                {livePreview.unitAmount.toFixed(2)} {selectedUnit.symbol}
              </Text>
            </View>
            <View style={styles.previewRow}>
              <Text style={[styles.previewLabel, { color: currentTheme.colors.textSecondary }]}>
                Cost per Unit:
              </Text>
              <Text style={[styles.previewValue, { color: currentTheme.colors.text }]}>
                {selectedCurrency.symbol} {costPerUnit.toFixed(3)}
              </Text>
            </View>
          </View>
        </View>

        {/* Save Button */}
        <ThemedButton
          title="Save Purchase"
          onPress={handleSavePurchase}
          icon="save"
          variant="primary"
          gradient={true}
          loading={isLoading}
          disabled={!currencyAmount || parseFloat(currencyAmount) <= 0}
          fullWidth={true}
        />
      </View>

      {/* Recent Purchases */}
      <View style={themedStyles.recentContainer}>
        <Text style={themedStyles.sectionTitle}>Recent Purchases</Text>
        {purchases.length > 0 ? (
          <FlatList
            data={purchases.slice(0, 10)} // Show last 10 purchases
            renderItem={renderPurchaseItem}
            keyExtractor={(item) => item.id?.toString() || Math.random().toString()}
            scrollEnabled={false}
            showsVerticalScrollIndicator={false}
            refreshControl={
              <RefreshControl
                refreshing={refreshing}
                onRefresh={onRefresh}
                tintColor={currentTheme.colors.primary}
              />
            }
          />
        ) : (
          <View style={[styles.emptyState, {
            backgroundColor: currentTheme.colors.background,
            borderColor: currentTheme.colors.surface
          }]}>
            <Icon name="shopping-cart" size={48} color={currentTheme.colors.textSecondary} />
            <Text style={[styles.emptyStateText, { color: currentTheme.colors.textSecondary }]}>
              No purchases yet
            </Text>
            <Text style={[styles.emptyStateSubtext, { color: currentTheme.colors.textSecondary }]}>
              Add your first purchase above to get started
            </Text>
          </View>
        )}
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  inputContainer: {
    margin: 15,
    padding: 20,
    borderRadius: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 20,
  },
  inputGroup: {
    marginBottom: 20,
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 8,
  },
  input: {
    borderWidth: 2,
    borderRadius: 10,
    padding: 15,
    fontSize: 16,
  },
  previewContainer: {
    borderRadius: 10,
    padding: 15,
    marginBottom: 20,
    borderWidth: 1,
  },
  previewTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 10,
  },
  previewContent: {
    gap: 8,
  },
  previewRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  previewLabel: {
    fontSize: 14,
  },
  previewValue: {
    fontSize: 14,
    fontWeight: '600',
  },
  recentContainer: {
    margin: 15,
    marginTop: 0,
    padding: 20,
    borderRadius: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  purchaseItem: {
    borderBottomWidth: 1,
    paddingVertical: 15,
  },
  purchaseHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  purchaseDate: {
    fontSize: 14,
    marginLeft: 8,
  },
  purchaseDetails: {
    marginLeft: 28,
  },
  purchaseAmount: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  purchaseRate: {
    fontSize: 14,
  },
  emptyState: {
    alignItems: 'center',
    padding: 40,
    borderRadius: 10,
    borderWidth: 2,
    borderStyle: 'dashed',
  },
  emptyStateText: {
    fontSize: 16,
    fontWeight: '600',
    marginTop: 15,
    marginBottom: 5,
  },
  emptyStateSubtext: {
    fontSize: 14,
    textAlign: 'center',
  },
});

export default PurchasesScreen;
