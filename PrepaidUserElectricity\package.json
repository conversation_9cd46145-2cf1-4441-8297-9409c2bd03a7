{"name": "prepaiduserelectricity", "version": "1.0.0", "main": "index.ts", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@react-native-async-storage/async-storage": "2.1.2", "@react-navigation/drawer": "^7.4.2", "@react-navigation/native": "^7.1.11", "@react-navigation/stack": "^7.3.4", "@reduxjs/toolkit": "^2.8.2", "expo": "~53.0.11", "expo-constants": "~17.1.6", "expo-device": "~7.1.4", "expo-linear-gradient": "~14.1.5", "expo-notifications": "~0.31.3", "expo-sqlite": "~15.2.12", "expo-status-bar": "~2.2.3", "react": "19.0.0", "react-native": "0.79.3", "react-native-chart-kit": "^6.12.0", "react-native-gesture-handler": "~2.24.0", "react-native-linear-gradient": "^2.8.3", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-svg": "15.11.2", "react-native-vector-icons": "^10.2.0", "react-redux": "^9.2.0", "victory-native": "^41.17.4"}, "devDependencies": {"@babel/core": "^7.25.2", "@testing-library/jest-native": "^5.4.3", "@testing-library/react-native": "^13.2.0", "@types/jest": "^30.0.0", "@types/react": "~19.0.10", "@types/react-native-vector-icons": "^6.4.18", "jest": "^30.0.0", "typescript": "~5.8.3"}, "private": true}