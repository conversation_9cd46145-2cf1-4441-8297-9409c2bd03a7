import { executeQuery, fetchQuery, fetchSingleQuery } from '../database/database';
import { Purchase, UsageRecord, HistoryEntry, TotalRecord, AppSettings } from '../types';

// Purchase operations
export const purchaseService = {
  // Add a new purchase
  addPurchase: async (purchase: Omit<Purchase, 'id'>): Promise<number> => {
    const query = `
      INSERT INTO purchases (currency_amount, unit_amount, currency_type, unit_type, cost_per_unit, purchase_date, notes)
      VALUES (?, ?, ?, ?, ?, ?, ?)
    `;
    const params = [
      purchase.currency_amount,
      purchase.unit_amount,
      purchase.currency_type,
      purchase.unit_type,
      purchase.cost_per_unit,
      purchase.purchase_date,
      purchase.notes || null,
    ];
    
    const result = await executeQuery(query, params);
    
    // Add to history
    await historyService.addHistoryEntry({
      type: 'purchase',
      reference_id: result.lastInsertRowId,
      amount: purchase.unit_amount,
      currency_value: purchase.currency_amount,
      unit_type: purchase.unit_type,
      currency_type: purchase.currency_type,
      date: purchase.purchase_date,
      description: `Purchase: ${purchase.currency_amount} ${purchase.currency_type} for ${purchase.unit_amount} ${purchase.unit_type}`,
    });
    
    return result.lastInsertRowId;
  },

  // Get all purchases
  getAllPurchases: async (): Promise<Purchase[]> => {
    const query = 'SELECT * FROM purchases ORDER BY purchase_date DESC';
    return await fetchQuery(query);
  },

  // Get purchases by date range
  getPurchasesByDateRange: async (startDate: string, endDate: string): Promise<Purchase[]> => {
    const query = `
      SELECT * FROM purchases 
      WHERE purchase_date BETWEEN ? AND ? 
      ORDER BY purchase_date DESC
    `;
    return await fetchQuery(query, [startDate, endDate]);
  },

  // Get total purchases for a period
  getTotalPurchases: async (startDate: string, endDate: string): Promise<{ currency: number; units: number }> => {
    const query = `
      SELECT 
        SUM(currency_amount) as total_currency,
        SUM(unit_amount) as total_units
      FROM purchases 
      WHERE purchase_date BETWEEN ? AND ?
    `;
    const result = await fetchSingleQuery(query, [startDate, endDate]);
    return {
      currency: result?.total_currency || 0,
      units: result?.total_units || 0,
    };
  },

  // Delete a purchase
  deletePurchase: async (id: number): Promise<void> => {
    await executeQuery('DELETE FROM purchases WHERE id = ?', [id]);
    await executeQuery('DELETE FROM history WHERE type = ? AND reference_id = ?', ['purchase', id]);
  },

  // Clear all purchases
  clearAllPurchases: async (): Promise<void> => {
    await executeQuery('DELETE FROM purchases');
    await executeQuery('DELETE FROM history WHERE type = ?', ['purchase']);
  },
};

// Usage operations
export const usageService = {
  // Add a new usage record
  addUsageRecord: async (usage: Omit<UsageRecord, 'id'>): Promise<number> => {
    const query = `
      INSERT INTO usage_records (previous_units, current_units, usage_amount, unit_type, record_date, notes)
      VALUES (?, ?, ?, ?, ?, ?)
    `;
    const params = [
      usage.previous_units,
      usage.current_units,
      usage.usage_amount,
      usage.unit_type,
      usage.record_date,
      usage.notes || null,
    ];
    
    const result = await executeQuery(query, params);
    
    // Add to history
    await historyService.addHistoryEntry({
      type: 'usage',
      reference_id: result.lastInsertRowId,
      amount: usage.usage_amount,
      unit_type: usage.unit_type,
      date: usage.record_date,
      description: `Usage: ${usage.usage_amount} ${usage.unit_type} (${usage.previous_units} → ${usage.current_units})`,
    });
    
    return result.lastInsertRowId;
  },

  // Get all usage records
  getAllUsageRecords: async (): Promise<UsageRecord[]> => {
    const query = 'SELECT * FROM usage_records ORDER BY record_date DESC';
    return await fetchQuery(query);
  },

  // Get usage records by date range
  getUsageByDateRange: async (startDate: string, endDate: string): Promise<UsageRecord[]> => {
    const query = `
      SELECT * FROM usage_records 
      WHERE record_date BETWEEN ? AND ? 
      ORDER BY record_date DESC
    `;
    return await fetchQuery(query, [startDate, endDate]);
  },

  // Get total usage for a period
  getTotalUsage: async (startDate: string, endDate: string): Promise<number> => {
    const query = `
      SELECT SUM(usage_amount) as total_usage
      FROM usage_records 
      WHERE record_date BETWEEN ? AND ?
    `;
    const result = await fetchSingleQuery(query, [startDate, endDate]);
    return result?.total_usage || 0;
  },

  // Get latest usage record
  getLatestUsageRecord: async (): Promise<UsageRecord | null> => {
    const query = 'SELECT * FROM usage_records ORDER BY record_date DESC LIMIT 1';
    return await fetchSingleQuery(query);
  },

  // Calculate usage since last recording
  calculateUsageSinceLastRecording: async (currentUnits: number): Promise<number> => {
    const latestRecord = await usageService.getLatestUsageRecord();
    if (!latestRecord) {
      return 0;
    }
    return latestRecord.current_units - currentUnits;
  },

  // Delete a usage record
  deleteUsageRecord: async (id: number): Promise<void> => {
    await executeQuery('DELETE FROM usage_records WHERE id = ?', [id]);
    await executeQuery('DELETE FROM history WHERE type = ? AND reference_id = ?', ['usage', id]);
  },

  // Clear all usage records
  clearAllUsageRecords: async (): Promise<void> => {
    await executeQuery('DELETE FROM usage_records');
    await executeQuery('DELETE FROM history WHERE type = ?', ['usage']);
  },
};

// History operations
export const historyService = {
  // Add a history entry
  addHistoryEntry: async (entry: Omit<HistoryEntry, 'id'>): Promise<number> => {
    const query = `
      INSERT INTO history (type, reference_id, amount, currency_value, unit_type, currency_type, date, description)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    `;
    const params = [
      entry.type,
      entry.reference_id,
      entry.amount,
      entry.currency_value || null,
      entry.unit_type,
      entry.currency_type || null,
      entry.date,
      entry.description || null,
    ];
    
    const result = await executeQuery(query, params);
    return result.lastInsertRowId;
  },

  // Get all history entries
  getAllHistory: async (): Promise<HistoryEntry[]> => {
    const query = 'SELECT * FROM history ORDER BY date DESC';
    return await fetchQuery(query);
  },

  // Get history by type
  getHistoryByType: async (type: 'purchase' | 'usage'): Promise<HistoryEntry[]> => {
    const query = 'SELECT * FROM history WHERE type = ? ORDER BY date DESC';
    return await fetchQuery(query, [type]);
  },

  // Get history by date range
  getHistoryByDateRange: async (startDate: string, endDate: string): Promise<HistoryEntry[]> => {
    const query = `
      SELECT * FROM history 
      WHERE date BETWEEN ? AND ? 
      ORDER BY date DESC
    `;
    return await fetchQuery(query, [startDate, endDate]);
  },

  // Clear all history
  clearHistory: async (): Promise<void> => {
    await executeQuery('DELETE FROM history');
  },
};

// Totals operations
export const totalsService = {
  // Update or create totals for a period
  updateTotals: async (total: Omit<TotalRecord, 'id' | 'created_at' | 'updated_at'>): Promise<void> => {
    // Check if record exists
    const existingQuery = `
      SELECT id FROM totals 
      WHERE period_type = ? AND period_start = ? AND period_end = ?
    `;
    const existing = await fetchSingleQuery(existingQuery, [
      total.period_type,
      total.period_start,
      total.period_end,
    ]);

    if (existing) {
      // Update existing record
      const updateQuery = `
        UPDATE totals 
        SET total_purchases_currency = ?, total_purchases_units = ?, total_usage_units = ?, 
            currency_type = ?, unit_type = ?, updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
      `;
      await executeQuery(updateQuery, [
        total.total_purchases_currency,
        total.total_purchases_units,
        total.total_usage_units,
        total.currency_type,
        total.unit_type,
        existing.id,
      ]);
    } else {
      // Create new record
      const insertQuery = `
        INSERT INTO totals (period_type, period_start, period_end, total_purchases_currency, 
                           total_purchases_units, total_usage_units, currency_type, unit_type)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
      `;
      await executeQuery(insertQuery, [
        total.period_type,
        total.period_start,
        total.period_end,
        total.total_purchases_currency,
        total.total_purchases_units,
        total.total_usage_units,
        total.currency_type,
        total.unit_type,
      ]);
    }
  },

  // Get totals by period type
  getTotalsByPeriod: async (periodType: 'daily' | 'weekly' | 'monthly'): Promise<TotalRecord[]> => {
    const query = 'SELECT * FROM totals WHERE period_type = ? ORDER BY period_start DESC';
    return await fetchQuery(query, [periodType]);
  },

  // Get current week totals
  getCurrentWeekTotals: async (): Promise<TotalRecord | null> => {
    const now = new Date();
    const startOfWeek = new Date(now.setDate(now.getDate() - now.getDay()));
    const endOfWeek = new Date(now.setDate(startOfWeek.getDate() + 6));
    
    const query = `
      SELECT * FROM totals 
      WHERE period_type = 'weekly' AND period_start = ? AND period_end = ?
    `;
    return await fetchSingleQuery(query, [
      startOfWeek.toISOString().split('T')[0],
      endOfWeek.toISOString().split('T')[0],
    ]);
  },

  // Get current month totals
  getCurrentMonthTotals: async (): Promise<TotalRecord | null> => {
    const now = new Date();
    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
    const endOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0);
    
    const query = `
      SELECT * FROM totals 
      WHERE period_type = 'monthly' AND period_start = ? AND period_end = ?
    `;
    return await fetchSingleQuery(query, [
      startOfMonth.toISOString().split('T')[0],
      endOfMonth.toISOString().split('T')[0],
    ]);
  },
};

// Settings operations
export const settingsService = {
  // Set a setting
  setSetting: async (key: string, value: string): Promise<void> => {
    const query = `
      INSERT OR REPLACE INTO settings (key, value, updated_at)
      VALUES (?, ?, CURRENT_TIMESTAMP)
    `;
    await executeQuery(query, [key, value]);
  },

  // Get a setting
  getSetting: async (key: string, defaultValue?: string): Promise<string | null> => {
    const query = 'SELECT value FROM settings WHERE key = ?';
    const result = await fetchSingleQuery(query, [key]);
    return result?.value || defaultValue || null;
  },

  // Get all settings
  getAllSettings: async (): Promise<AppSettings[]> => {
    const query = 'SELECT * FROM settings ORDER BY key';
    return await fetchQuery(query);
  },

  // Delete a setting
  deleteSetting: async (key: string): Promise<void> => {
    await executeQuery('DELETE FROM settings WHERE key = ?', [key]);
  },

  // Clear all settings
  clearAllSettings: async (): Promise<void> => {
    await executeQuery('DELETE FROM settings');
  },
};
