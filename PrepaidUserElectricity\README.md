# Prepaid User - Electricity Tracker

A modern React Native app for tracking prepaid electricity usage, purchases, and managing units efficiently with beautiful charts and smart notifications.

## Features

- 📊 **Modern Dashboard** - Beautiful dial visualization and real-time unit tracking
- 💰 **Purchase Management** - Track electricity purchases with currency conversion
- 📈 **Usage Analytics** - Detailed charts showing daily, weekly, and monthly patterns
- 📱 **Smart Notifications** - Daily reminders and low unit warnings
- 🎨 **5 Beautiful Themes** - Light, Dark, Blue, Green, and Purple themes
- 💾 **Local Storage** - All data stored securely on your device
- 🔄 **Data Management** - Export, import, and reset functionality

## Tech Stack

- **React Native** with Expo
- **TypeScript** for type safety
- **Redux Toolkit** for state management
- **SQLite** for local data storage
- **Victory Native** for charts
- **Tamagui** for UI components
- **React Navigation** for navigation

## Installation

1. Clone the repository
2. Install dependencies:
   ```bash
   npm install
   ```
3. Start the development server:
   ```bash
   npm start
   ```

## Building for Production

### Prerequisites
- Install EAS CLI: `npm install -g @expo/eas-cli`
- Create an Expo account and login: `eas login`

### Android APK (for testing)
```bash
eas build --platform android --profile preview
```

### Production Builds
```bash
# Android AAB for Google Play Store
eas build --platform android --profile production

# iOS for App Store
eas build --platform ios --profile production
```

### Submitting to Stores
```bash
# Submit to Google Play Store
eas submit --platform android

# Submit to Apple App Store
eas submit --platform ios
```

## Configuration

Before building, update the following in `app.json`:
- `expo.extra.eas.projectId` - Your EAS project ID
- `expo.ios.bundleIdentifier` - Your iOS bundle identifier
- `expo.android.package` - Your Android package name

## App Store Requirements

### Android (Google Play Store)
- Target API level 34 (Android 14)
- App bundle (AAB) format
- Privacy policy URL
- App content rating

### iOS (Apple App Store)
- iOS 13.4+ support
- App Store Connect metadata
- Privacy policy
- App review guidelines compliance

## Privacy & Permissions

The app requests the following permissions:
- **Notifications** - For daily reminders and low unit alerts
- **Storage** - For local data persistence

All data is stored locally on the device. No personal information is transmitted to external servers.

## Support

For support or feature requests, please contact the development team.

## License

Copyright © 2024 Prepaid User Electricity. All rights reserved.
