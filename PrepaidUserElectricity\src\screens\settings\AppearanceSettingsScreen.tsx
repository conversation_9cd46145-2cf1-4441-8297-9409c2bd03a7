import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { useAppDispatch, useAppSelector } from '../../store';
import { saveSettings } from '../../store/slices/settingsSlice';
import { useTheme } from '../../context/ThemeContext';
import { DEFAULT_THEMES, DEFAULT_FONTS } from '../../storage/asyncStorage';
import ThemedButton from '../../components/ThemedButton';
import { LinearGradient } from 'expo-linear-gradient';

const AppearanceSettingsScreen = () => {
  const dispatch = useAppDispatch();
  const { currentTheme, changeTheme } = useTheme();
  const settingsState = useAppSelector((state) => state.settings);

  const [selectedThemeId, setSelectedThemeId] = useState(settingsState.config.selectedTheme);
  const [selectedFontId, setSelectedFontId] = useState(settingsState.config.selectedFont);
  const [isLoading, setIsLoading] = useState(false);

  // Get available themes and fonts from storage
  const themes = DEFAULT_THEMES;
  const fonts = DEFAULT_FONTS;

  useEffect(() => {
    setSelectedThemeId(settingsState.config.selectedTheme);
    setSelectedFontId(settingsState.config.selectedFont);
  }, [settingsState.config.selectedTheme, settingsState.config.selectedFont]);

  const handleSaveAppearance = async () => {
    try {
      setIsLoading(true);

      // Update settings in Redux store
      await dispatch(saveSettings({
        selectedTheme: selectedThemeId,
        selectedFont: selectedFontId,
      })).unwrap();

      // Apply theme immediately
      await changeTheme(selectedThemeId);

      Alert.alert('Success', 'Appearance settings saved successfully');
    } catch (error) {
      console.error('Error saving appearance settings:', error);
      Alert.alert('Error', 'Failed to save appearance settings');
    } finally {
      setIsLoading(false);
    }
  };

  const handleThemePreview = async (themeId: string) => {
    setSelectedThemeId(themeId);
    // Apply theme immediately for live preview
    try {
      await changeTheme(themeId);
    } catch (error) {
      console.error('Error applying theme preview:', error);
    }
  };

  const renderThemePreview = (theme: any) => (
    <TouchableOpacity
      key={theme.id}
      style={[
        styles.themeCard,
        selectedThemeId === theme.id && styles.selectedThemeCard,
        { backgroundColor: theme.colors.background },
      ]}
      onPress={() => handleThemePreview(theme.id)}
    >
      <View style={styles.themeHeader}>
        <Text style={styles.themeIcon}>{theme.icon}</Text>
        <Text style={[styles.themeName, { color: theme.colors.primary }]}>
          {theme.name}
        </Text>
      </View>

      <View style={styles.themePreview}>
        {/* Primary Color Bar */}
        <LinearGradient
          colors={theme.gradients.primary}
          style={styles.colorBar}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 0 }}
        />

        {/* Secondary Color Bar */}
        <LinearGradient
          colors={theme.gradients.secondary}
          style={styles.colorBar}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 0 }}
        />

        {/* Sample UI Elements */}
        <View style={styles.sampleElements}>
          <LinearGradient
            colors={theme.gradients.primary}
            style={styles.sampleButton}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 0 }}
          >
            <Text style={styles.sampleButtonText}>Button</Text>
          </LinearGradient>
          <View style={[styles.sampleCard, {
            borderColor: theme.colors.secondary,
            backgroundColor: theme.colors.surface
          }]}>
            <Text style={[styles.sampleText, { color: theme.colors.text }]}>
              Sample Card
            </Text>
          </View>
        </View>
      </View>

      {selectedThemeId === theme.id && (
        <View style={styles.selectedIndicator}>
          <Icon name="check-circle" size={20} color={theme.colors.primary} />
        </View>
      )}
    </TouchableOpacity>
  );

  const themedStyles = {
    container: {
      ...styles.container,
      backgroundColor: currentTheme.colors.background,
    },
    section: {
      ...styles.section,
      backgroundColor: currentTheme.colors.surface,
    },
    sectionTitle: {
      ...styles.sectionTitle,
      color: currentTheme.colors.text,
    },
    sectionSubtitle: {
      ...styles.sectionSubtitle,
      color: currentTheme.colors.textSecondary,
    },
  };

  return (
    <ScrollView style={themedStyles.container}>
      {/* Theme Selection */}
      <View style={themedStyles.section}>
        <Text style={themedStyles.sectionTitle}>
          <Icon name="palette" size={18} color={currentTheme.colors.accent} /> Themes
        </Text>
        <Text style={themedStyles.sectionSubtitle}>
          Choose a theme that matches your style
        </Text>

        <View style={styles.themesGrid}>
          {themes.map(renderThemePreview)}
        </View>
      </View>

      {/* Font Selection */}
      <View style={themedStyles.section}>
        <Text style={themedStyles.sectionTitle}>
          <Icon name="text-fields" size={18} color={currentTheme.colors.secondary} /> Fonts
        </Text>
        <Text style={themedStyles.sectionSubtitle}>
          Select your preferred font family
        </Text>

        <View style={styles.fontsContainer}>
          {fonts.map((font) => (
            <TouchableOpacity
              key={font}
              style={[
                styles.fontOption,
                {
                  backgroundColor: currentTheme.colors.background,
                  borderColor: selectedFontId === font ? currentTheme.colors.primary : currentTheme.colors.surface,
                },
                selectedFontId === font && {
                  backgroundColor: currentTheme.colors.primary + '20',
                  borderColor: currentTheme.colors.primary,
                },
              ]}
              onPress={() => setSelectedFontId(font)}
            >
              <View style={styles.fontContent}>
                <Text style={[styles.fontName, {
                  color: currentTheme.colors.text,
                  fontFamily: font === 'System' ? undefined : font
                }]}>
                  {font}
                </Text>
                <Text style={[styles.fontPreview, {
                  color: currentTheme.colors.textSecondary,
                  fontFamily: font === 'System' ? undefined : font
                }]}>
                  The quick brown fox jumps over the lazy dog
                </Text>
              </View>
              {selectedFontId === font && (
                <Icon name="check-circle" size={20} color={currentTheme.colors.primary} />
              )}
            </TouchableOpacity>
          ))}
        </View>
      </View>

      {/* Button Styles Preview */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>
          <Icon name="touch-app" size={18} color="#34C759" /> Button Styles
        </Text>
        <Text style={styles.sectionSubtitle}>
          Preview of gradient buttons with selected theme
        </Text>
        
        <View style={styles.buttonPreviewContainer}>
          <TouchableOpacity style={styles.previewButton}>
            <Icon name="add" size={20} color="#fff" />
            <Text style={styles.previewButtonText}>Primary Button</Text>
          </TouchableOpacity>
          
          <TouchableOpacity style={styles.previewButtonSecondary}>
            <Icon name="edit" size={20} color="#007AFF" />
            <Text style={styles.previewButtonSecondaryText}>Secondary Button</Text>
          </TouchableOpacity>
          
          <TouchableOpacity style={styles.previewButtonOutline}>
            <Icon name="delete" size={20} color="#FF3B30" />
            <Text style={styles.previewButtonOutlineText}>Outline Button</Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Card Styles Preview */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>
          <Icon name="view-module" size={18} color="#FF6B35" /> Card Styles
        </Text>
        <Text style={styles.sectionSubtitle}>
          Preview of card components with selected theme
        </Text>
        
        <View style={styles.cardPreviewContainer}>
          <View style={styles.previewCard}>
            <View style={styles.previewCardHeader}>
              <Icon name="flash-on" size={24} color="#007AFF" />
              <Text style={styles.previewCardTitle}>Sample Card</Text>
            </View>
            <Text style={styles.previewCardContent}>
              This is how cards will look with your selected theme and font.
            </Text>
            <View style={styles.previewCardFooter}>
              <Text style={styles.previewCardValue}>123.45 kWh</Text>
            </View>
          </View>
        </View>
      </View>

      {/* Live Preview Note */}
      <View style={styles.noteSection}>
        <Icon name="info" size={20} color="#5AC8FA" />
        <Text style={styles.noteText}>
          Changes will be applied immediately when you save. You can preview the theme by looking at the samples above.
        </Text>
      </View>

      {/* Save Button */}
      <View style={styles.saveContainer}>
        <ThemedButton
          title="Apply Theme"
          onPress={handleSaveAppearance}
          icon="save"
          variant="primary"
          gradient={true}
          loading={isLoading}
          fullWidth={true}
        />
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  section: {
    margin: 15,
    padding: 20,
    borderRadius: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 5,
  },
  sectionSubtitle: {
    fontSize: 14,
    marginBottom: 20,
  },
  themesGrid: {
    gap: 15,
  },
  themeCard: {
    borderRadius: 12,
    padding: 15,
    borderWidth: 2,
    borderColor: '#E5E5EA',
  },
  selectedThemeCard: {
    borderWidth: 3,
  },
  themeHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
  },
  themeIcon: {
    fontSize: 20,
    marginRight: 8,
  },
  themeName: {
    fontSize: 16,
    fontWeight: '600',
  },
  themePreview: {
    gap: 8,
  },
  colorBar: {
    height: 8,
    borderRadius: 4,
  },
  sampleElements: {
    flexDirection: 'row',
    gap: 10,
    marginTop: 8,
  },
  sampleButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
  },
  sampleButtonText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '600',
  },
  sampleCard: {
    flex: 1,
    padding: 8,
    borderRadius: 6,
    borderWidth: 1,
  },
  sampleText: {
    fontSize: 12,
    fontWeight: '500',
  },
  selectedIndicator: {
    position: 'absolute',
    top: 10,
    right: 10,
  },
  fontsContainer: {
    gap: 10,
  },
  fontOption: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 15,
    borderRadius: 10,
    borderWidth: 2,
  },
  fontContent: {
    flex: 1,
  },
  fontName: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  fontPreview: {
    fontSize: 14,
  },
  buttonPreviewContainer: {
    gap: 12,
  },
  previewButton: {
    backgroundColor: '#007AFF',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 12,
    borderRadius: 8,
    gap: 8,
  },
  previewButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '600',
  },
  previewButtonSecondary: {
    backgroundColor: '#E3F2FD',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 12,
    borderRadius: 8,
    gap: 8,
  },
  previewButtonSecondaryText: {
    color: '#007AFF',
    fontSize: 14,
    fontWeight: '600',
  },
  previewButtonOutline: {
    backgroundColor: 'transparent',
    borderWidth: 2,
    borderColor: '#FF3B30',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 10,
    borderRadius: 8,
    gap: 8,
  },
  previewButtonOutlineText: {
    color: '#FF3B30',
    fontSize: 14,
    fontWeight: '600',
  },
  cardPreviewContainer: {
    marginTop: 10,
  },
  previewCard: {
    backgroundColor: '#F8F9FA',
    padding: 15,
    borderRadius: 10,
    borderWidth: 1,
    borderColor: '#E5E5EA',
  },
  previewCardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
  },
  previewCardTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1D1D1F',
    marginLeft: 8,
  },
  previewCardContent: {
    fontSize: 14,
    color: '#666',
    marginBottom: 10,
  },
  previewCardFooter: {
    alignItems: 'flex-end',
  },
  previewCardValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#007AFF',
  },
  noteSection: {
    backgroundColor: '#E3F2FD',
    margin: 15,
    padding: 15,
    borderRadius: 10,
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  noteText: {
    flex: 1,
    fontSize: 14,
    color: '#1565C0',
    marginLeft: 10,
    lineHeight: 20,
  },
  saveContainer: {
    margin: 15,
    marginBottom: 30,
  },
  saveButton: {
    backgroundColor: '#007AFF',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 15,
    borderRadius: 10,
    gap: 8,
  },
  saveButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default AppearanceSettingsScreen;
