import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TextInput,
  TouchableOpacity,
  Alert,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { useNavigation } from '@react-navigation/native';
import { useAppDispatch } from '../store';
import { saveSettings } from '../store/slices/settingsSlice';
import { setCurrentUnits } from '../store/slices/dashboardSlice';
import { addPurchase } from '../store/slices/purchasesSlice';
import { useTheme } from '../context/ThemeContext';
import { DEFAULT_CURRENCIES, DEFAULT_UNITS } from '../storage/asyncStorage';
import { Purchase } from '../types';

const InitialSetupScreen = () => {
  const navigation = useNavigation();
  const dispatch = useAppDispatch();
  const { currentTheme } = useTheme();
  const [currentStep, setCurrentStep] = useState(1);
  const [isLoading, setIsLoading] = useState(false);
  const [setupData, setSetupData] = useState({
    currentUnits: '',
    currencyAmount: '',
    selectedCurrency: 'USD',
    selectedUnit: 'kWh',
    costPerUnit: '0.15',
    thresholdLimit: '20',
  });

  const totalSteps = 3;

  // Use default currencies and units from storage
  const currencies = DEFAULT_CURRENCIES;
  const units = DEFAULT_UNITS;

  const handleNext = () => {
    if (currentStep === 1) {
      if (!setupData.currentUnits || parseFloat(setupData.currentUnits) < 0) {
        Alert.alert('Error', 'Please enter a valid current unit value');
        return;
      }
    } else if (currentStep === 2) {
      if (!setupData.currencyAmount || parseFloat(setupData.currencyAmount) <= 0) {
        Alert.alert('Error', 'Please enter a valid currency amount');
        return;
      }
    }

    if (currentStep < totalSteps) {
      setCurrentStep(currentStep + 1);
    } else {
      handleFinishSetup();
    }
  };

  const handleBack = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleFinishSetup = async () => {
    try {
      setIsLoading(true);

      // Find selected currency and unit objects
      const selectedCurrencyObj = currencies.find(c => c.code === setupData.selectedCurrency) || currencies[0];
      const selectedUnitObj = units.find(u => u.symbol === setupData.selectedUnit) || units[0];

      // Save settings to Redux and storage
      await dispatch(saveSettings({
        costPerUnit: parseFloat(setupData.costPerUnit),
        thresholdLimit: parseFloat(setupData.thresholdLimit),
        selectedCurrency: selectedCurrencyObj,
        selectedUnit: selectedUnitObj,
        isFirstLaunch: false,
      })).unwrap();

      // Set initial dashboard data
      dispatch(setCurrentUnits(parseFloat(setupData.currentUnits)));

      // Add initial purchase if provided
      if (setupData.currencyAmount && parseFloat(setupData.currencyAmount) > 0) {
        const calculatedUnits = parseFloat(setupData.currencyAmount) / parseFloat(setupData.costPerUnit);

        const initialPurchase: Omit<Purchase, 'id'> = {
          currency_amount: parseFloat(setupData.currencyAmount),
          unit_amount: calculatedUnits,
          currency_type: selectedCurrencyObj.code,
          unit_type: selectedUnitObj.symbol,
          cost_per_unit: parseFloat(setupData.costPerUnit),
          purchase_date: new Date().toISOString(),
          notes: 'Initial setup purchase',
        };

        await dispatch(addPurchase(initialPurchase)).unwrap();
      }

      Alert.alert(
        'Setup Complete',
        'Your app has been configured successfully!',
        [
          {
            text: 'Get Started',
            onPress: () => navigation.reset({
              index: 0,
              routes: [{ name: 'Main' as never }],
            }),
          },
        ]
      );
    } catch (error) {
      console.error('Error completing setup:', error);
      Alert.alert('Error', 'Failed to complete setup. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const renderProgressBar = () => (
    <View style={styles.progressContainer}>
      <View style={styles.progressBar}>
        <View
          style={[
            styles.progressFill,
            { width: `${(currentStep / totalSteps) * 100}%` },
          ]}
        />
      </View>
      <Text style={styles.progressText}>
        Step {currentStep} of {totalSteps}
      </Text>
    </View>
  );

  const renderStep1 = () => (
    <View style={styles.stepContainer}>
      <View style={styles.stepHeader}>
        <Icon name="flash-on" size={60} color="#007AFF" />
        <Text style={styles.stepTitle}>Welcome to Prepaid Electricity</Text>
        <Text style={styles.stepSubtitle}>
          Let's set up your current electricity units
        </Text>
      </View>

      <View style={styles.inputSection}>
        <Text style={styles.inputLabel}>
          Current Units on Your Meter
        </Text>
        <TextInput
          style={styles.input}
          value={setupData.currentUnits}
          onChangeText={(value) =>
            setSetupData({ ...setupData, currentUnits: value })
          }
          placeholder="Enter current reading (e.g., 150.5)"
          keyboardType="numeric"
          placeholderTextColor="#999"
        />
        <Text style={styles.inputHint}>
          Check your electricity meter and enter the current reading
        </Text>
      </View>

      <View style={styles.unitSelection}>
        <Text style={styles.selectionLabel}>Select Unit Type:</Text>
        <View style={styles.optionsGrid}>
          {units.map((unit) => (
            <TouchableOpacity
              key={unit.symbol}
              style={[
                styles.optionButton,
                {
                  backgroundColor: currentTheme.colors.surface,
                  borderColor: setupData.selectedUnit === unit.symbol ? currentTheme.colors.primary : currentTheme.colors.surface,
                },
                setupData.selectedUnit === unit.symbol && {
                  backgroundColor: currentTheme.colors.primary + '20',
                },
              ]}
              onPress={() =>
                setSetupData({ ...setupData, selectedUnit: unit.symbol })
              }
            >
              <Text style={styles.optionSymbol}>⚡</Text>
              <Text
                style={[
                  styles.optionText,
                  { color: setupData.selectedUnit === unit.symbol ? currentTheme.colors.primary : currentTheme.colors.textSecondary },
                  setupData.selectedUnit === unit.symbol && { fontWeight: '600' },
                ]}
              >
                {unit.name}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>
    </View>
  );

  const renderStep2 = () => (
    <View style={styles.stepContainer}>
      <View style={styles.stepHeader}>
        <Icon name="attach-money" size={60} color="#34C759" />
        <Text style={styles.stepTitle}>Currency Configuration</Text>
        <Text style={styles.stepSubtitle}>
          Set up your currency and recent purchase
        </Text>
      </View>

      <View style={styles.currencySelection}>
        <Text style={styles.selectionLabel}>Select Currency:</Text>
        <View style={styles.optionsGrid}>
          {currencies.map((currency) => (
            <TouchableOpacity
              key={currency.code}
              style={[
                styles.optionButton,
                {
                  backgroundColor: currentTheme.colors.surface,
                  borderColor: setupData.selectedCurrency === currency.code ? currentTheme.colors.primary : currentTheme.colors.surface,
                },
                setupData.selectedCurrency === currency.code && {
                  backgroundColor: currentTheme.colors.primary + '20',
                },
              ]}
              onPress={() =>
                setSetupData({ ...setupData, selectedCurrency: currency.code })
              }
            >
              <Text style={styles.optionSymbol}>{currency.symbol}</Text>
              <Text
                style={[
                  styles.optionText,
                  { color: setupData.selectedCurrency === currency.code ? currentTheme.colors.primary : currentTheme.colors.textSecondary },
                  setupData.selectedCurrency === currency.code && { fontWeight: '600' },
                ]}
              >
                {currency.name}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>

      <View style={styles.inputSection}>
        <Text style={styles.inputLabel}>
          Recent Purchase Amount (Optional)
        </Text>
        <TextInput
          style={styles.input}
          value={setupData.currencyAmount}
          onChangeText={(value) =>
            setSetupData({ ...setupData, currencyAmount: value })
          }
          placeholder="Enter amount spent (e.g., 25.00)"
          keyboardType="numeric"
          placeholderTextColor="#999"
        />
        <Text style={styles.inputHint}>
          This helps calculate your cost per unit automatically
        </Text>
      </View>
    </View>
  );

  const renderStep3 = () => (
    <View style={styles.stepContainer}>
      <View style={styles.stepHeader}>
        <Icon name="settings" size={60} color="#FF9500" />
        <Text style={styles.stepTitle}>Final Configuration</Text>
        <Text style={styles.stepSubtitle}>
          Set your preferences and thresholds
        </Text>
      </View>

      <View style={styles.inputSection}>
        <Text style={styles.inputLabel}>Cost Per Unit</Text>
        <TextInput
          style={styles.input}
          value={setupData.costPerUnit}
          onChangeText={(value) =>
            setSetupData({ ...setupData, costPerUnit: value })
          }
          placeholder="0.15"
          keyboardType="numeric"
          placeholderTextColor="#999"
        />
        <Text style={styles.inputHint}>
          {setupData.selectedCurrency} per {setupData.selectedUnit}
        </Text>
      </View>

      <View style={styles.inputSection}>
        <Text style={styles.inputLabel}>Low Units Threshold</Text>
        <TextInput
          style={styles.input}
          value={setupData.thresholdLimit}
          onChangeText={(value) =>
            setSetupData({ ...setupData, thresholdLimit: value })
          }
          placeholder="20"
          keyboardType="numeric"
          placeholderTextColor="#999"
        />
        <Text style={styles.inputHint}>
          Get warned when units drop below this value
        </Text>
      </View>

      <View style={styles.summaryContainer}>
        <Text style={styles.summaryTitle}>Setup Summary:</Text>
        <Text style={styles.summaryItem}>
          Current Units: {setupData.currentUnits} {setupData.selectedUnit}
        </Text>
        <Text style={styles.summaryItem}>
          Currency: {setupData.selectedCurrency}
        </Text>
        <Text style={styles.summaryItem}>
          Cost per Unit: {setupData.selectedCurrency} {setupData.costPerUnit}
        </Text>
        <Text style={styles.summaryItem}>
          Threshold: {setupData.thresholdLimit} {setupData.selectedUnit}
        </Text>
      </View>
    </View>
  );

  return (
    <View style={styles.container}>
      {renderProgressBar()}
      
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {currentStep === 1 && renderStep1()}
        {currentStep === 2 && renderStep2()}
        {currentStep === 3 && renderStep3()}
      </ScrollView>

      <View style={styles.buttonContainer}>
        {currentStep > 1 && (
          <TouchableOpacity style={styles.backButton} onPress={handleBack}>
            <Icon name="arrow-back" size={20} color="#007AFF" />
            <Text style={styles.backButtonText}>Back</Text>
          </TouchableOpacity>
        )}
        
        <TouchableOpacity style={styles.nextButton} onPress={handleNext}>
          <Text style={styles.nextButtonText}>
            {currentStep === totalSteps ? 'Finish Setup' : 'Next'}
          </Text>
          <Icon 
            name={currentStep === totalSteps ? 'check' : 'arrow-forward'} 
            size={20} 
            color="#fff" 
          />
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F2F2F7',
  },
  progressContainer: {
    backgroundColor: '#FFFFFF',
    padding: 20,
    paddingTop: 50,
  },
  progressBar: {
    height: 4,
    backgroundColor: '#E5E5EA',
    borderRadius: 2,
    marginBottom: 10,
  },
  progressFill: {
    height: '100%',
    backgroundColor: '#007AFF',
    borderRadius: 2,
  },
  progressText: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
  },
  content: {
    flex: 1,
  },
  stepContainer: {
    padding: 20,
  },
  stepHeader: {
    alignItems: 'center',
    marginBottom: 30,
  },
  stepTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1D1D1F',
    textAlign: 'center',
    marginTop: 15,
    marginBottom: 10,
  },
  stepSubtitle: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    lineHeight: 22,
  },
  inputSection: {
    marginBottom: 25,
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1D1D1F',
    marginBottom: 8,
  },
  input: {
    borderWidth: 2,
    borderColor: '#007AFF',
    borderRadius: 10,
    padding: 15,
    fontSize: 16,
    backgroundColor: '#FFFFFF',
  },
  inputHint: {
    fontSize: 14,
    color: '#666',
    marginTop: 5,
    fontStyle: 'italic',
  },
  selectionLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1D1D1F',
    marginBottom: 15,
  },
  unitSelection: {
    marginBottom: 25,
  },
  currencySelection: {
    marginBottom: 25,
  },
  optionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 10,
  },
  optionButton: {
    flex: 1,
    minWidth: '45%',
    backgroundColor: '#FFFFFF',
    padding: 15,
    borderRadius: 10,
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#E5E5EA',
  },
  selectedOption: {
    backgroundColor: '#E3F2FD',
    borderColor: '#007AFF',
  },
  optionSymbol: {
    fontSize: 24,
    marginBottom: 8,
  },
  optionText: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
  },
  selectedOptionText: {
    color: '#007AFF',
    fontWeight: '600',
  },
  summaryContainer: {
    backgroundColor: '#F0F9FF',
    padding: 15,
    borderRadius: 10,
    borderWidth: 1,
    borderColor: '#007AFF',
  },
  summaryTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#007AFF',
    marginBottom: 10,
  },
  summaryItem: {
    fontSize: 14,
    color: '#1D1D1F',
    marginBottom: 5,
  },
  buttonContainer: {
    flexDirection: 'row',
    padding: 20,
    backgroundColor: '#FFFFFF',
    gap: 15,
  },
  backButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 15,
    borderRadius: 10,
    backgroundColor: '#F2F2F7',
    gap: 8,
  },
  backButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#007AFF',
  },
  nextButton: {
    flex: 2,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 15,
    borderRadius: 10,
    backgroundColor: '#007AFF',
    gap: 8,
  },
  nextButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
  },
});

export default InitialSetupScreen;
