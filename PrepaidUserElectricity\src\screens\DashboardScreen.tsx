import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  RefreshControl,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { useNavigation } from '@react-navigation/native';
import { useAppDispatch, useAppSelector } from '../store';
import { loadDashboardData } from '../store/slices/dashboardSlice';
import { loadSettings } from '../store/slices/settingsSlice';

import ModernDial from '../components/ModernDial';
import QuickActionButton from '../components/QuickActionButton';
import StatsCard from '../components/StatsCard';
import { notificationService } from '../services/notificationService';
import { useTheme } from '../context/ThemeContext';

const DashboardScreen = () => {
  const navigation = useNavigation();
  const dispatch = useAppDispatch();
  const { currentTheme } = useTheme();
  const [refreshing, setRefreshing] = useState(false);

  // Get data from Redux store
  const dashboardState = useAppSelector((state) => state.dashboard);
  const settingsState = useAppSelector((state) => state.settings);

  const {
    currentUnits,
    usageSinceLastRecording,
    weeklyTotal,
    monthlyTotal,
    isLowUnitsWarning,
    lastRecordingDate,
  } = dashboardState;

  const {
    config: {
      thresholdLimit,
      selectedUnit,
      selectedCurrency,
      costPerUnit,
    }
  } = settingsState;

  const isLowUnits = currentUnits <= thresholdLimit;

  useEffect(() => {
    // Load settings and dashboard data on component mount
    const loadData = async () => {
      try {
        await dispatch(loadSettings()).unwrap();
        await dispatch(loadDashboardData()).unwrap();
      } catch (error) {
        console.error('Error loading initial data:', error);
      }
    };

    loadData();
  }, [dispatch]);

  useEffect(() => {
    // Show low units warning if threshold is reached
    if (isLowUnits && currentUnits > 0) {
      Alert.alert(
        'Low Units Warning',
        `Your current units (${currentUnits} ${selectedUnit.symbol}) are below the threshold limit of ${thresholdLimit} ${selectedUnit.symbol}.`,
        [{ text: 'OK' }]
      );

      // Send notification
      notificationService.sendLowUnitsWarning(
        currentUnits,
        thresholdLimit,
        selectedUnit.symbol
      );
    }
  }, [isLowUnits, currentUnits, thresholdLimit, selectedUnit]);

  const onRefresh = async () => {
    setRefreshing(true);
    try {
      await dispatch(loadDashboardData()).unwrap();
    } catch (error) {
      console.error('Error refreshing dashboard:', error);
      Alert.alert('Error', 'Failed to refresh dashboard data');
    } finally {
      setRefreshing(false);
    }
  };

  const quickActions = [
    {
      title: 'Add Purchase',
      icon: 'add-shopping-cart',
      color: currentTheme.colors.success,
      onPress: () => navigation.navigate('Purchases' as never),
    },
    {
      title: 'Record Usage',
      icon: 'trending-up',
      color: currentTheme.colors.primary,
      onPress: () => navigation.navigate('Usage' as never),
    },
    {
      title: 'View History',
      icon: 'history',
      color: currentTheme.colors.accent,
      onPress: () => navigation.navigate('History' as never),
    },
  ];

  const themedStyles = {
    container: {
      ...styles.container,
      backgroundColor: currentTheme.colors.background,
    },
    dialContainer: {
      ...styles.dialContainer,
      backgroundColor: currentTheme.colors.surface,
    },
    sectionTitle: {
      ...styles.sectionTitle,
      color: currentTheme.colors.text,
    },
    quickActionsGrid: {
      ...styles.quickActionsGrid,
      backgroundColor: currentTheme.colors.surface,
    },
  };

  return (
    <ScrollView
      style={themedStyles.container}
      refreshControl={
        <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
      }
    >
      {/* Current Units Display with Modern Dial */}
      <View style={themedStyles.dialContainer}>
        <Text style={themedStyles.sectionTitle}>Current Units</Text>
        <ModernDial
          value={currentUnits}
          maxValue={200}
          size={200}
          strokeWidth={20}
          color={isLowUnits ? currentTheme.colors.error : currentTheme.colors.primary}
          backgroundColor={currentTheme.colors.surface}
          gradient={isLowUnits ? [currentTheme.colors.error, '#FF6B6B'] : currentTheme.gradients.primary}
          label="Units Remaining"
          unit={selectedUnit.symbol}
        />
        
        {isLowUnits && (
          <View style={[styles.warningContainer, { backgroundColor: currentTheme.colors.error + '20' }]}>
            <Icon name="warning" size={20} color={currentTheme.colors.error} />
            <Text style={[styles.warningText, { color: currentTheme.colors.error }]}>Low Units Warning!</Text>
          </View>
        )}
      </View>

      {/* Usage Since Last Recording */}
      <View style={styles.usageContainer}>
        <StatsCard
          title="Usage Since Last Recording"
          value={usageSinceLastRecording}
          unit={selectedUnit.symbol}
          icon="trending-up"
          color={currentTheme.colors.accent}
          subtitle={`Last recorded: ${new Date(lastRecordingDate).toLocaleDateString()}`}
        />
      </View>

      {/* Weekly and Monthly Totals */}
      <View style={styles.totalsContainer}>
        <View style={styles.totalRow}>
          <StatsCard
            title="Weekly Total"
            value={weeklyTotal.usage}
            unit={selectedUnit.symbol}
            icon="date-range"
            color={currentTheme.colors.success}
            subtitle={`${selectedCurrency.symbol}${weeklyTotal.currency.toFixed(2)}`}
            style={styles.halfCard}
          />
          <StatsCard
            title="Monthly Total"
            value={monthlyTotal.usage}
            unit={selectedUnit.symbol}
            icon="calendar-today"
            color={currentTheme.colors.secondary}
            subtitle={`${selectedCurrency.symbol}${monthlyTotal.currency.toFixed(2)}`}
            style={styles.halfCard}
          />
        </View>
      </View>

      {/* Quick Actions */}
      <View style={styles.quickActionsContainer}>
        <Text style={themedStyles.sectionTitle}>Quick Actions</Text>
        <View style={themedStyles.quickActionsGrid}>
          {quickActions.map((action, index) => (
            <QuickActionButton
              key={index}
              title={action.title}
              icon={action.icon}
              color={action.color}
              onPress={action.onPress}
            />
          ))}
        </View>
      </View>

      {/* Cost Information */}
      <View style={styles.costContainer}>
        <StatsCard
          title="Cost Per Unit"
          value={costPerUnit}
          unit={`${selectedCurrency.symbol}/${selectedUnit.symbol}`}
          icon="attach-money"
          color={currentTheme.colors.warning}
          subtitle="Current rate"
        />
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  dialContainer: {
    margin: 15,
    padding: 20,
    borderRadius: 15,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 15,
  },
  warningContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 10,
    borderRadius: 8,
    marginTop: 15,
  },
  warningText: {
    fontWeight: '600',
    marginLeft: 8,
  },
  usageContainer: {
    margin: 15,
    marginTop: 0,
  },
  totalsContainer: {
    margin: 15,
    marginTop: 0,
  },
  totalRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  halfCard: {
    flex: 0.48,
  },
  quickActionsContainer: {
    margin: 15,
    marginTop: 0,
  },
  quickActionsGrid: {
    borderRadius: 15,
    padding: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  costContainer: {
    margin: 15,
    marginTop: 0,
    marginBottom: 30,
  },
});

export default DashboardScreen;
