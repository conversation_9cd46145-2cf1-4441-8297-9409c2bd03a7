import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import Svg, { Circle, Defs, LinearGradient, Stop } from 'react-native-svg';
import { ModernDialProps } from '../types';
import { useTheme } from '../context/ThemeContext';

const ModernDial: React.FC<ModernDialProps> = ({
  value,
  maxValue,
  size,
  strokeWidth,
  color,
  backgroundColor,
  gradient,
  label,
  unit,
}) => {
  const { currentTheme } = useTheme();
  const radius = (size - strokeWidth) / 2;
  const circumference = 2 * Math.PI * radius;
  const percentage = Math.min(value / maxValue, 1);
  const strokeDasharray = circumference;
  const strokeDashoffset = circumference * (1 - percentage);

  const center = size / 2;

  // Use theme colors if not provided
  const dialColor = color || currentTheme.colors.primary;
  const dialBackgroundColor = backgroundColor || currentTheme.colors.surface;
  const dialGradient = gradient || currentTheme.gradients.primary;

  return (
    <View style={[styles.container, { width: size, height: size }]}>
      <Svg width={size} height={size} style={styles.svg}>
        <Defs>
          <LinearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <Stop offset="0%" stopColor={dialGradient[0]} />
            <Stop offset="100%" stopColor={dialGradient[1]} />
          </LinearGradient>
        </Defs>

        {/* Background Circle */}
        <Circle
          cx={center}
          cy={center}
          r={radius}
          stroke={dialBackgroundColor}
          strokeWidth={strokeWidth}
          fill="transparent"
        />

        {/* Progress Circle */}
        <Circle
          cx={center}
          cy={center}
          r={radius}
          stroke={gradient ? 'url(#gradient)' : dialColor}
          strokeWidth={strokeWidth}
          fill="transparent"
          strokeDasharray={strokeDasharray}
          strokeDashoffset={strokeDashoffset}
          strokeLinecap="round"
          transform={`rotate(-90 ${center} ${center})`}
        />
      </Svg>
      
      {/* Center Content */}
      <View style={styles.centerContent}>
        <Text style={[styles.value, { color: dialColor }]}>
          {value.toFixed(1)}
        </Text>
        <Text style={[styles.unit, { color: currentTheme.colors.textSecondary }]}>{unit}</Text>
        <Text style={[styles.label, { color: currentTheme.colors.textSecondary }]}>{label}</Text>
        <Text style={[styles.percentage, { color: dialColor }]}>
          {(percentage * 100).toFixed(0)}%
        </Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'relative',
    alignItems: 'center',
    justifyContent: 'center',
  },
  svg: {
    position: 'absolute',
  },
  centerContent: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  value: {
    fontSize: 32,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  unit: {
    fontSize: 14,
    marginBottom: 8,
  },
  label: {
    fontSize: 12,
    textAlign: 'center',
    marginBottom: 4,
  },
  percentage: {
    fontSize: 16,
    fontWeight: '600',
  },
});

export default ModernDial;
