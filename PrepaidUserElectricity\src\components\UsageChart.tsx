import React from 'react';
import { View, Text, StyleSheet, Dimensions } from 'react-native';
import { LineChart, BarChart } from 'react-native-chart-kit';
import { ChartData, UsageRecord } from '../types';
import { useTheme } from '../context/ThemeContext';

interface UsageChartProps {
  data: UsageRecord[];
  chartType: 'line' | 'area' | 'bar';
  period: 'week' | 'month';
  color: string;
  gradient?: string[];
}

const { width } = Dimensions.get('window');
const chartWidth = width - 60;

const UsageChart: React.FC<UsageChartProps> = ({
  data,
  chartType,
  period,
  color,
  gradient,
}) => {
  const { currentTheme } = useTheme();

  // Use theme colors if not provided
  const chartColor = color || currentTheme.colors.primary;
  // Transform usage data for chart
  const transformDataForChart = () => {
    if (!data || data.length === 0) {
      return {
        labels: [],
        datasets: [{ data: [] }],
      };
    }

    const records = data.slice(0, period === 'week' ? 7 : 30).reverse();
    const labels = records.map(record => {
      const date = new Date(record.record_date);
      return period === 'week'
        ? date.toLocaleDateString('en', { weekday: 'short' })
        : date.getDate().toString();
    });

    const chartData = records.map(record => record.usage_amount);

    return {
      labels,
      datasets: [
        {
          data: chartData,
          color: (opacity = 1) => chartColor + Math.round(opacity * 255).toString(16).padStart(2, '0'),
          strokeWidth: 3,
        },
      ],
    };
  };

  const chartData = transformDataForChart();

  if (chartData.datasets[0].data.length === 0) {
    return (
      <View style={[styles.emptyChart, {
        backgroundColor: currentTheme.colors.background,
        borderColor: currentTheme.colors.surface
      }]}>
        <Text style={[styles.emptyChartText, { color: currentTheme.colors.textSecondary }]}>
          No data available for chart
        </Text>
        <Text style={[styles.emptyChartSubtext, { color: currentTheme.colors.textSecondary }]}>
          Record some usage to see your patterns
        </Text>
      </View>
    );
  }

  const dataValues = chartData.datasets[0].data;
  const maxValue = Math.max(...dataValues);
  const minValue = Math.min(...dataValues);

  const chartConfig = {
    backgroundColor: currentTheme.colors.surface,
    backgroundGradientFrom: currentTheme.colors.surface,
    backgroundGradientTo: currentTheme.colors.surface,
    decimalPlaces: 1,
    color: (opacity = 1) => chartColor + Math.round(opacity * 255).toString(16).padStart(2, '0'),
    labelColor: (opacity = 1) => currentTheme.colors.textSecondary + Math.round(opacity * 255).toString(16).padStart(2, '0'),
    style: {
      borderRadius: 16,
    },
    propsForDots: {
      r: '6',
      strokeWidth: '2',
      stroke: chartColor,
    },
  };

  const renderChart = () => {
    const commonProps = {
      data: chartData,
      width: chartWidth,
      height: 200,
      chartConfig,
      bezier: true,
      style: {
        marginVertical: 8,
        borderRadius: 16,
      },
    };

    switch (chartType) {
      case 'area':
        // Use LineChart with filled area effect
        return (
          <LineChart
            {...commonProps}
            withDots={true}
            withShadow={true}
            withInnerLines={true}
            withOuterLines={true}
            bezier
          />
        );

      case 'bar':
        return (
          <BarChart
            {...commonProps}
            yAxisLabel=""
            yAxisSuffix=""
            showValuesOnTopOfBars={true}
            withInnerLines={true}
            fromZero={true}
          />
        );

      default: // line
        return (
          <LineChart
            {...commonProps}
            withDots={true}
            withShadow={true}
            withInnerLines={true}
            withOuterLines={true}
          />
        );
    }
  };

  return (
    <View style={[styles.container, { backgroundColor: currentTheme.colors.surface }]}>
      <View style={styles.header}>
        <Text style={[styles.title, { color: currentTheme.colors.text }]}>
          {period === 'week' ? 'Weekly' : 'Monthly'} Usage Pattern
        </Text>
        <Text style={[styles.subtitle, { color: currentTheme.colors.textSecondary }]}>
          {chartType.charAt(0).toUpperCase() + chartType.slice(1)} Chart
        </Text>
      </View>
      
      <View style={styles.chartContainer}>
        {renderChart()}
      </View>

      <View style={[styles.stats, { borderTopColor: currentTheme.colors.surface }]}>
        <View style={styles.statItem}>
          <Text style={[styles.statLabel, { color: currentTheme.colors.textSecondary }]}>Average</Text>
          <Text style={[styles.statValue, { color: chartColor }]}>
            {(dataValues.reduce((sum, d) => sum + d, 0) / dataValues.length).toFixed(1)}
          </Text>
        </View>
        <View style={styles.statItem}>
          <Text style={[styles.statLabel, { color: currentTheme.colors.textSecondary }]}>Highest</Text>
          <Text style={[styles.statValue, { color: chartColor }]}>
            {maxValue.toFixed(1)}
          </Text>
        </View>
        <View style={styles.statItem}>
          <Text style={[styles.statLabel, { color: currentTheme.colors.textSecondary }]}>Lowest</Text>
          <Text style={[styles.statValue, { color: chartColor }]}>
            {minValue.toFixed(1)}
          </Text>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    borderRadius: 15,
    padding: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  header: {
    marginBottom: 15,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 14,
  },
  chartContainer: {
    alignItems: 'center',
    marginBottom: 15,
  },
  stats: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingTop: 15,
    borderTopWidth: 1,
  },
  statItem: {
    alignItems: 'center',
  },
  statLabel: {
    fontSize: 12,
    marginBottom: 4,
  },
  statValue: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  emptyChart: {
    alignItems: 'center',
    padding: 40,
    borderRadius: 10,
    borderWidth: 2,
    borderStyle: 'dashed',
  },
  emptyChartText: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 5,
  },
  emptyChartSubtext: {
    fontSize: 14,
    textAlign: 'center',
  },
});

export default UsageChart;
